version: '3.8'

services:
  # PostgreSQL 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: wuhr-ai-ops-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: wuhr_ai_ops
      POSTGRES_USER: wuhr_admin
      POSTGRES_PASSWORD: wuhr_secure_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-scripts:/docker-entrypoint-initdb.d
      - ./docker/postgres-conf/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./data:/docker-entrypoint-initdb.d/data:ro
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
    networks:
      - wuhr-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U wuhr_admin -d wuhr_ai_ops"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin 数据库管理界面（可选）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: wuhr-ai-ops-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin_password_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - wuhr-network
    depends_on:
      postgres:
        condition: service_healthy

  # Redis 缓存服务（为未来功能准备）
  redis:
    image: redis:7-alpine
    container_name: wuhr-ai-ops-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password_2024
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wuhr-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local

networks:
  wuhr-network:
    driver: bridge
