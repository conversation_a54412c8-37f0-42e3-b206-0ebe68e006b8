{"name": "wuhr-ai-ops", "version": "1.0.0", "description": "Wuhr AI Ops - 面向运维工程师的AI助手Web系统", "main": "index.js", "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:migrate:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:test": "npx ts-node lib/config/db-test.ts", "db:seed": "npx ts-node lib/config/db-seed.ts", "migrate": "node scripts/migrate-database.js migrate", "migrate:validate": "node scripts/migrate-database.js validate", "migrate:status": "node scripts/migrate-database.js status", "migrate:dry-run": "node scripts/migrate-database.js migrate --dry-run", "migrate:validate-only": "node scripts/migrate-database.js migrate --validate-only", "db:start": "./docker/start-database.sh", "db:stop": "./docker/manage-database.sh stop", "db:restart": "./docker/manage-database.sh restart", "db:status": "./docker/manage-database.sh status", "db:logs": "./docker/manage-database.sh logs", "db:connect": "./docker/manage-database.sh connect", "db:backup": "./docker/manage-database.sh backup", "db:clean": "./docker/manage-database.sh clean", "db:reset": "./docker/manage-database.sh reset", "deploy": "./scripts/quick-deploy.sh", "deploy:clean": "./scripts/quick-deploy.sh --clean", "deploy:help": "./scripts/quick-deploy.sh --help"}, "keywords": ["AI", "运维", "Gemini", "DevOps"], "author": "Wuhr AI", "license": "MIT", "dependencies": {"@ant-design/charts": "^2.4.0", "@ant-design/icons": "^5.2.0", "@prisma/client": "^6.10.1", "@types/ioredis": "^4.28.10", "@types/node-ssh": "^7.0.6", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "@uiw/react-textarea-code-editor": "^3.1.1", "antd": "^5.12.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "codemirror": "^5.65.19", "cron-parser": "^5.3.0", "cronstrue": "^2.61.0", "dotenv": "^17.0.0", "ioredis": "^5.6.1", "jose": "^5.10.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.300.0", "next": "^14.0.0", "node-fetch": "^3.3.2", "node-ssh": "^13.2.1", "pg": "^8.16.3", "playwright": "^1.54.1", "postcss": "^8.4.0", "prisma": "^6.10.1", "react": "^18.0.0", "react-codemirror2": "^8.0.1", "react-dom": "^18.0.0", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "redis": "^5.5.6", "remark-gfm": "^4.0.1", "simple-git": "^3.28.0", "socket.io-client": "^4.7.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "tailwindcss": "^3.3.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "ignore-loader": "^0.1.2", "typescript": "^5.0.0"}}