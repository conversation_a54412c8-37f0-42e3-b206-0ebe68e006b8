# 数据库连接池优化配置
# 复制此文件为 .env.local 并根据实际情况调整参数

# 基本数据库连接配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wuhr_ai_ops
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_SSL=false

# 连接池配置 - 根据服务器性能和负载调整
# 最大连接数：建议设置为 CPU核心数 * 2 + 有效磁盘数
DB_MAX_CONNECTIONS=20

# 连接超时：获取连接的最大等待时间（毫秒）
DB_CONNECTION_TIMEOUT=10000

# 空闲超时：连接在池中保持空闲的最大时间（毫秒）
DB_IDLE_TIMEOUT=60000

# 查询超时：单个查询的最大执行时间（毫秒）
DB_QUERY_TIMEOUT=30000

# 事务超时：事务的最大执行时间（毫秒）
DB_TRANSACTION_TIMEOUT=60000

# 连接池监控配置
# 启用连接池监控日志
DB_POOL_MONITORING=true

# 监控日志级别：debug, info, warn, error
DB_POOL_LOG_LEVEL=info

# 连接池健康检查间隔（秒）
DB_HEALTH_CHECK_INTERVAL=30

# 连接泄漏检测配置
# 启用连接泄漏检测
DB_LEAK_DETECTION=true

# 连接泄漏阈值：操作超过此时间视为潜在泄漏（毫秒）
DB_LEAK_THRESHOLD=60000

# 最大活跃操作数：超过此数量将触发告警
DB_MAX_ACTIVE_OPERATIONS=50

# 性能优化配置
# 启用查询缓存
DB_QUERY_CACHE=true

# 批量操作大小限制
DB_BATCH_SIZE=100

# 并发查询限制
DB_CONCURRENT_QUERIES=10

# 开发环境特定配置
# 启用详细的SQL日志（仅开发环境）
DB_LOG_QUERIES=false

# 启用慢查询日志
DB_LOG_SLOW_QUERIES=true

# 慢查询阈值（毫秒）
DB_SLOW_QUERY_THRESHOLD=1000

# 生产环境建议配置
# 对于生产环境，建议使用以下配置：
# DB_MAX_CONNECTIONS=50
# DB_CONNECTION_TIMEOUT=5000
# DB_IDLE_TIMEOUT=30000
# DB_QUERY_TIMEOUT=15000
# DB_TRANSACTION_TIMEOUT=30000
# DB_POOL_LOG_LEVEL=warn
# DB_LOG_QUERIES=false
# DB_LOG_SLOW_QUERIES=true
# DB_SLOW_QUERY_THRESHOLD=500

# 高负载环境建议配置
# 对于高负载环境，建议使用以下配置：
# DB_MAX_CONNECTIONS=100
# DB_CONNECTION_TIMEOUT=3000
# DB_IDLE_TIMEOUT=15000
# DB_QUERY_TIMEOUT=10000
# DB_TRANSACTION_TIMEOUT=20000
# DB_HEALTH_CHECK_INTERVAL=15
# DB_LEAK_THRESHOLD=30000
# DB_MAX_ACTIVE_OPERATIONS=100

# 监控和告警配置
# 启用性能监控
DB_PERFORMANCE_MONITORING=true

# 启用告警通知
DB_ALERT_ENABLED=true

# 告警阈值配置
DB_ALERT_CONNECTION_USAGE_THRESHOLD=80  # 连接使用率告警阈值（百分比）
DB_ALERT_QUERY_DURATION_THRESHOLD=5000  # 查询时间告警阈值（毫秒）
DB_ALERT_IDLE_TRANSACTION_THRESHOLD=10  # 空闲事务数量告警阈值

# 自动恢复配置
# 启用自动连接恢复
DB_AUTO_RECOVERY=true

# 连接失败重试次数
DB_RETRY_ATTEMPTS=3

# 重试间隔（毫秒）
DB_RETRY_INTERVAL=5000

# 备份和维护配置
# 启用自动清理
DB_AUTO_CLEANUP=true

# 清理间隔（小时）
DB_CLEANUP_INTERVAL=24

# 保留日志天数
DB_LOG_RETENTION_DAYS=7
