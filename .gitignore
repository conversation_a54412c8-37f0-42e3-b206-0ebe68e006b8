node_modules
# Keep environment variables out of version control
.env
.env.local
.env.production

# Prisma generated files
/lib/generated/prisma

# Data directories
/data/backups/*.sql
/data/backups/*.sql.gz
/data/deployments/*

# Keep data directory structure but ignore content
!/data/backups/.gitkeep
!/data/deployments/.gitkeep

# Build outputs
.next/
dist/
build/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# But keep app/servers/logs (it's a page, not log files)
!app/servers/logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Ignore nested git repositories
deployments/projects/voicechat2
