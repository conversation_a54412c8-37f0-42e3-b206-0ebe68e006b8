# Database Configuration
# PostgreSQL connection settings (matching docker-compose.yml)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=wuhr_ai_ops
DB_USER=wuhr_admin
DB_PASSWORD=wuhr_secure_password_2024
DB_SSL=false

# Connection Pool Settings - Conservative settings to prevent connection exhaustion
DB_MIN_CONNECTIONS=2
DB_MAX_CONNECTIONS=5
DB_INITIAL_CONNECTIONS=2
DB_CONNECTION_TIMEOUT=10000
DB_IDLE_TIMEOUT=300000

# Encryption Settings
ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Git Authentication - 完全依赖用户配置，不使用环境变量

# Prisma Database URLs
# Primary connection URL for application usage with connection pool limits
DATABASE_URL="postgresql://wuhr_admin:wuhr_secure_password_2024@localhost:5432/wuhr_ai_ops?sslmode=disable&connection_limit=5&pool_timeout=20&connect_timeout=10"

# Direct URL for migrations (optional, used for connection pooling)
DIRECT_URL="postgresql://wuhr_admin:wuhr_secure_password_2024@localhost:5432/wuhr_ai_ops?sslmode=disable&connection_limit=3&pool_timeout=20&connect_timeout=10"

# Application Environment
NODE_ENV=development

# JWT Configuration
JWT_SECRET=wuhr_ai_ops_jwt_secret_2024_development
JWT_ACCESS_TOKEN_EXPIRY=15m
JWT_REFRESH_TOKEN_EXPIRY=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Redis配置（用于缓存和会话）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_2024
REDIS_URL=redis://:redis_password_2024@localhost:6379

# 日志级别
LOG_LEVEL=info

# 部署配置
DEPLOYMENT_TIMEOUT=300000
