# PostgreSQL Configuration for Wuhr AI Ops
# Optimized for development environment with connection pool management

# Connection Settings
listen_addresses = '*'                  # 监听所有地址
max_connections = 50                    # 最大连接数设置为50（为15个应用连接留足余量）
shared_buffers = 128MB                  # 共享缓冲区
effective_cache_size = 256MB            # 有效缓存大小

# Connection Pool Settings
max_prepared_transactions = 50         # 最大预处理事务数
max_locks_per_transaction = 64          # 每个事务的最大锁数

# Memory Settings
work_mem = 4MB                          # 工作内存
maintenance_work_mem = 64MB             # 维护工作内存

# Logging Settings
log_statement = 'none'                  # 减少日志输出
log_min_duration_statement = 1000      # 只记录超过1秒的查询
log_connections = off                   # 关闭连接日志
log_disconnections = off                # 关闭断开连接日志

# Performance Settings
checkpoint_completion_target = 0.9      # 检查点完成目标
wal_buffers = 16MB                      # WAL缓冲区
default_statistics_target = 100         # 默认统计目标

# Timeout Settings
statement_timeout = 30000               # 语句超时 (30秒)
lock_timeout = 10000                    # 锁超时 (10秒)
idle_in_transaction_session_timeout = 60000  # 事务中空闲会话超时 (60秒)

# Autovacuum Settings
autovacuum = on                         # 启用自动清理
autovacuum_max_workers = 3              # 自动清理最大工作进程数
